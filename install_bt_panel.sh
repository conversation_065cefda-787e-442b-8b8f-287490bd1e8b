#!/bin/bash

# {{RIPER-5:
# Action: Added
# Task: #49gt4z3G7AR8fdDwowwv2F | Time: $(date '+%Y-%m-%d %H:%M:%S')
# Reason: 创建宝塔面板自动安装脚本，支持多Linux发行版
# Principle: 模块化设计，错误处理，安全配置
# Architecture_Note: [AR] 采用检测-安装-配置三阶段模式
# Quality_Check: [LD] 支持CentOS/Ubuntu/Debian，包含安全加固
# }}

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
BT_PANEL_URL="https://download.bt.cn/install/install_panel.sh"
LOG_FILE="/var/log/bt_install.log"
CONFIG_DIR="/etc/bt_manager"

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

# Print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_status $RED "Error: This script must be run as root"
        exit 1
    fi
}

# Detect OS and version
detect_os() {
    if [[ -f /etc/redhat-release ]]; then
        OS="centos"
        VERSION=$(grep -oE '[0-9]+\.[0-9]+' /etc/redhat-release | head -1)
    elif [[ -f /etc/lsb-release ]]; then
        OS="ubuntu"
        VERSION=$(grep DISTRIB_RELEASE /etc/lsb-release | cut -d'=' -f2)
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
        VERSION=$(cat /etc/debian_version)
    else
        print_status $RED "Unsupported operating system"
        exit 1
    fi
    
    log "INFO" "Detected OS: $OS $VERSION"
}

# Check system requirements
check_requirements() {
    print_status $BLUE "Checking system requirements..."
    
    # Check memory
    local mem_total=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [[ $mem_total -lt 512 ]]; then
        print_status $YELLOW "Warning: Low memory detected (${mem_total}MB). Recommended: 512MB+"
    fi
    
    # Check disk space
    local disk_free=$(df / | awk 'NR==2{printf "%.0f", $4/1024}')
    if [[ $disk_free -lt 1024 ]]; then
        print_status $RED "Error: Insufficient disk space (${disk_free}MB free). Required: 1GB+"
        exit 1
    fi
    
    # Check network connectivity
    if ! ping -c 1 download.bt.cn &> /dev/null; then
        print_status $RED "Error: Cannot reach BT Panel download server"
        exit 1
    fi
    
    log "INFO" "System requirements check passed"
}

# Install dependencies
install_dependencies() {
    print_status $BLUE "Installing dependencies..."
    
    case $OS in
        "centos")
            yum update -y
            yum install -y wget curl vim unzip tar
            ;;
        "ubuntu"|"debian")
            apt-get update -y
            apt-get install -y wget curl vim unzip tar
            ;;
    esac
    
    log "INFO" "Dependencies installed successfully"
}

# Download and install BT Panel
install_bt_panel() {
    print_status $BLUE "Downloading and installing BT Panel..."
    
    # Create temporary directory
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # Download installation script
    if ! wget -O install_panel.sh "$BT_PANEL_URL"; then
        print_status $RED "Failed to download BT Panel installation script"
        exit 1
    fi
    
    # Make executable and run
    chmod +x install_panel.sh
    
    # Run installation with auto-confirm
    echo "y" | bash install_panel.sh
    
    # Cleanup
    cd /
    rm -rf "$temp_dir"
    
    log "INFO" "BT Panel installation completed"
}

# Configure security settings
configure_security() {
    print_status $BLUE "Configuring security settings..."
    
    # Create config directory
    mkdir -p "$CONFIG_DIR"
    
    # Generate random panel port (8888-9999)
    local panel_port=$((RANDOM % 1112 + 8888))
    
    # Configure firewall
    case $OS in
        "centos")
            if command -v firewall-cmd &> /dev/null; then
                firewall-cmd --permanent --add-port=${panel_port}/tcp
                firewall-cmd --reload
            fi
            ;;
        "ubuntu"|"debian")
            if command -v ufw &> /dev/null; then
                ufw allow ${panel_port}/tcp
            fi
            ;;
    esac
    
    # Change default panel port
    if [[ -f /www/server/panel/data/port.pl ]]; then
        echo "$panel_port" > /www/server/panel/data/port.pl
        /etc/init.d/bt restart
    fi
    
    # Save configuration
    cat > "$CONFIG_DIR/panel.conf" << EOF
PANEL_PORT=$panel_port
INSTALL_DATE=$(date '+%Y-%m-%d %H:%M:%S')
OS_INFO=$OS $VERSION
EOF
    
    log "INFO" "Security configuration completed. Panel port: $panel_port"
}

# Get panel information
get_panel_info() {
    print_status $GREEN "Getting panel information..."
    
    # Get panel URL and credentials
    if [[ -f /www/server/panel/data/default.pl ]]; then
        local panel_info=$(/www/server/panel/tools.py panel)
        echo "$panel_info" | tee -a "$LOG_FILE"
    fi
    
    # Display configuration
    if [[ -f "$CONFIG_DIR/panel.conf" ]]; then
        print_status $GREEN "Configuration saved to: $CONFIG_DIR/panel.conf"
        cat "$CONFIG_DIR/panel.conf"
    fi
}

# Main installation function
main() {
    print_status $GREEN "Starting BT Panel installation..."
    log "INFO" "Installation started"
    
    check_root
    detect_os
    check_requirements
    install_dependencies
    install_bt_panel
    configure_security
    get_panel_info
    
    print_status $GREEN "BT Panel installation completed successfully!"
    print_status $YELLOW "Please save the panel login information displayed above."
    print_status $YELLOW "Log file: $LOG_FILE"
    
    log "INFO" "Installation completed successfully"
}

# Run main function
main "$@"
