#!/bin/bash

# {{RIPER-5:
# Action: Added
# Task: #aQ1vnTEqkVYPw2vepQon9H | Time: $(date '+%Y-%m-%d %H:%M:%S')
# Reason: 实现数据库备份、恢复、定时备份等核心功能
# Principle: 模块化设计，支持多数据库类型，错误处理完善
# Architecture_Note: [AR] 采用策略模式支持不同数据库类型
# Quality_Check: [LD] 支持MySQL/PostgreSQL，包含压缩和加密
# }}

set -euo pipefail

# Source common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../common/utils.sh"

# Database configuration
DB_BACKUP_DIR="/var/backups/databases"
DB_CONFIG_FILE="/etc/bt_manager/database.conf"
MAX_BACKUP_DAYS=30
COMPRESSION_LEVEL=6

# Initialize database manager
init_db_manager() {
    log "INFO" "Initializing database manager..."
    
    # Create backup directory
    mkdir -p "$DB_BACKUP_DIR"
    chmod 750 "$DB_BACKUP_DIR"
    
    # Create config file if not exists
    if [[ ! -f "$DB_CONFIG_FILE" ]]; then
        create_default_config
    fi
    
    log "INFO" "Database manager initialized"
}

# Create default configuration
create_default_config() {
    cat > "$DB_CONFIG_FILE" << 'EOF'
# Database Manager Configuration
# Supported types: mysql, postgresql, mongodb

[mysql]
host=localhost
port=3306
user=root
password=
databases=

[postgresql]
host=localhost
port=5432
user=postgres
password=
databases=

[mongodb]
host=localhost
port=27017
user=
password=
databases=

[backup]
compression=true
encryption=false
encryption_key=
max_backup_days=30
backup_schedule=daily
backup_time=02:00
EOF
    
    chmod 600 "$DB_CONFIG_FILE"
    log "INFO" "Default configuration created: $DB_CONFIG_FILE"
}

# Read configuration
read_config() {
    local section=$1
    local key=$2
    local default_value=${3:-""}
    
    if [[ -f "$DB_CONFIG_FILE" ]]; then
        local value=$(awk -F'=' -v section="[$section]" -v key="$key" '
            $0 == section { in_section = 1; next }
            /^\[/ { in_section = 0; next }
            in_section && $1 == key { print $2; exit }
        ' "$DB_CONFIG_FILE" | tr -d ' ')
        echo "${value:-$default_value}"
    else
        echo "$default_value"
    fi
}

# MySQL backup function
backup_mysql() {
    local db_name=$1
    local backup_file=$2
    
    local host=$(read_config "mysql" "host" "localhost")
    local port=$(read_config "mysql" "port" "3306")
    local user=$(read_config "mysql" "user" "root")
    local password=$(read_config "mysql" "password")
    
    log "INFO" "Starting MySQL backup for database: $db_name"
    
    # Build mysqldump command
    local dump_cmd="mysqldump"
    dump_cmd+=" --host=$host"
    dump_cmd+=" --port=$port"
    dump_cmd+=" --user=$user"
    
    if [[ -n "$password" ]]; then
        dump_cmd+=" --password=$password"
    fi
    
    dump_cmd+=" --single-transaction"
    dump_cmd+=" --routines"
    dump_cmd+=" --triggers"
    dump_cmd+=" --events"
    dump_cmd+=" --hex-blob"
    dump_cmd+=" --opt"
    dump_cmd+=" $db_name"
    
    # Execute backup
    if eval "$dump_cmd" > "$backup_file.sql"; then
        log "INFO" "MySQL backup completed: $backup_file.sql"
        return 0
    else
        log "ERROR" "MySQL backup failed for database: $db_name"
        return 1
    fi
}

# PostgreSQL backup function
backup_postgresql() {
    local db_name=$1
    local backup_file=$2
    
    local host=$(read_config "postgresql" "host" "localhost")
    local port=$(read_config "postgresql" "port" "5432")
    local user=$(read_config "postgresql" "user" "postgres")
    local password=$(read_config "postgresql" "password")
    
    log "INFO" "Starting PostgreSQL backup for database: $db_name"
    
    # Set environment variables
    export PGHOST=$host
    export PGPORT=$port
    export PGUSER=$user
    if [[ -n "$password" ]]; then
        export PGPASSWORD=$password
    fi
    
    # Execute backup
    if pg_dump --verbose --clean --no-owner --no-privileges "$db_name" > "$backup_file.sql"; then
        log "INFO" "PostgreSQL backup completed: $backup_file.sql"
        return 0
    else
        log "ERROR" "PostgreSQL backup failed for database: $db_name"
        return 1
    fi
}

# MongoDB backup function
backup_mongodb() {
    local db_name=$1
    local backup_file=$2
    
    local host=$(read_config "mongodb" "host" "localhost")
    local port=$(read_config "mongodb" "port" "27017")
    local user=$(read_config "mongodb" "user")
    local password=$(read_config "mongodb" "password")
    
    log "INFO" "Starting MongoDB backup for database: $db_name"
    
    # Build mongodump command
    local dump_cmd="mongodump"
    dump_cmd+=" --host $host:$port"
    dump_cmd+=" --db $db_name"
    dump_cmd+=" --out ${backup_file}_mongo"
    
    if [[ -n "$user" && -n "$password" ]]; then
        dump_cmd+=" --username $user --password $password"
    fi
    
    # Execute backup
    if eval "$dump_cmd"; then
        # Create archive
        tar -czf "${backup_file}.tar.gz" -C "${backup_file}_mongo" .
        rm -rf "${backup_file}_mongo"
        log "INFO" "MongoDB backup completed: ${backup_file}.tar.gz"
        return 0
    else
        log "ERROR" "MongoDB backup failed for database: $db_name"
        return 1
    fi
}

# Compress backup file
compress_backup() {
    local backup_file=$1
    
    if [[ $(read_config "backup" "compression" "true") == "true" ]]; then
        log "INFO" "Compressing backup file..."
        
        if gzip -$COMPRESSION_LEVEL "$backup_file"; then
            log "INFO" "Backup compressed: ${backup_file}.gz"
            echo "${backup_file}.gz"
        else
            log "ERROR" "Failed to compress backup file"
            echo "$backup_file"
        fi
    else
        echo "$backup_file"
    fi
}

# Encrypt backup file
encrypt_backup() {
    local backup_file=$1
    local encryption_key=$(read_config "backup" "encryption_key")
    
    if [[ $(read_config "backup" "encryption" "false") == "true" && -n "$encryption_key" ]]; then
        log "INFO" "Encrypting backup file..."
        
        if openssl enc -aes-256-cbc -salt -in "$backup_file" -out "${backup_file}.enc" -k "$encryption_key"; then
            rm "$backup_file"
            log "INFO" "Backup encrypted: ${backup_file}.enc"
            echo "${backup_file}.enc"
        else
            log "ERROR" "Failed to encrypt backup file"
            echo "$backup_file"
        fi
    else
        echo "$backup_file"
    fi
}

# Main backup function
backup_database() {
    local db_type=$1
    local db_name=$2
    local custom_name=${3:-""}
    
    init_db_manager
    
    # Generate backup filename
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_name="${custom_name:-${db_name}}_${timestamp}"
    local backup_file="${DB_BACKUP_DIR}/${backup_name}"
    
    log "INFO" "Starting backup for $db_type database: $db_name"
    
    # Perform backup based on database type
    case $db_type in
        "mysql")
            if backup_mysql "$db_name" "$backup_file"; then
                backup_file="${backup_file}.sql"
            else
                return 1
            fi
            ;;
        "postgresql")
            if backup_postgresql "$db_name" "$backup_file"; then
                backup_file="${backup_file}.sql"
            else
                return 1
            fi
            ;;
        "mongodb")
            if backup_mongodb "$db_name" "$backup_file"; then
                backup_file="${backup_file}.tar.gz"
            else
                return 1
            fi
            ;;
        *)
            log "ERROR" "Unsupported database type: $db_type"
            return 1
            ;;
    esac
    
    # Compress backup
    backup_file=$(compress_backup "$backup_file")
    
    # Encrypt backup
    backup_file=$(encrypt_backup "$backup_file")
    
    # Set permissions
    chmod 600 "$backup_file"
    
    log "INFO" "Backup completed successfully: $backup_file"
    echo "$backup_file"
}

# List available backups
list_backups() {
    local db_name=${1:-""}
    
    print_status $BLUE "Available database backups:"
    
    if [[ -n "$db_name" ]]; then
        find "$DB_BACKUP_DIR" -name "${db_name}_*" -type f | sort -r
    else
        find "$DB_BACKUP_DIR" -type f | sort -r
    fi
}

# Clean old backups
cleanup_backups() {
    local max_days=$(read_config "backup" "max_backup_days" "$MAX_BACKUP_DAYS")
    
    log "INFO" "Cleaning up backups older than $max_days days..."
    
    local deleted_count=$(find "$DB_BACKUP_DIR" -type f -mtime +$max_days -delete -print | wc -l)
    
    log "INFO" "Cleaned up $deleted_count old backup files"
}

# Export functions for external use
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Script is being run directly
    case "${1:-}" in
        "backup")
            backup_database "$2" "$3" "${4:-}"
            ;;
        "list")
            list_backups "${2:-}"
            ;;
        "cleanup")
            cleanup_backups
            ;;
        "init")
            init_db_manager
            ;;
        *)
            echo "Usage: $0 {backup|list|cleanup|init}"
            echo "  backup <type> <name> [custom_name]  - Backup database"
            echo "  list [db_name]                      - List backups"
            echo "  cleanup                             - Clean old backups"
            echo "  init                                - Initialize database manager"
            exit 1
            ;;
    esac
fi
